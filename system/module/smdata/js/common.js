/**
 * Common JavaScript for smdata module
 */

var smdata = {
    /**
     * Initialize module
     */
    init: function() {
        this.bindEvents();
        this.initDatePickers();
    },
    
    /**
     * Bind events
     */
    bindEvents: function() {
        // Sync button click
        $(document).on('click', '.smdata-sync-btn', function() {
            var $btn = $(this);
            var publicId = $btn.data('public-id');
            var syncType = $btn.data('sync-type');
            var startDate = $('#startDate').val() || $btn.data('start-date');
            var endDate = $('#endDate').val() || $btn.data('end-date');
            
            smdata.syncData(publicId, syncType, startDate, endDate, $btn);
        });
        
        // Filter form submit
        $(document).on('submit', '.smdata-filter-form', function(e) {
            e.preventDefault();
            var $form = $(this);
            var url = $form.attr('action');
            var data = $form.serialize();
            if (url.indexOf('?') === -1) {
                url += '?';
            } else {
                url += '&';
            }
            url += data;
            window.location.href = url;
        });
        
        // Export button click
        $(document).on('click', '.smdata-export-btn', function() {
            var $btn = $(this);
            var format = $btn.data('format') || 'csv';
            var params = smdata.getFilterParams();
            
            smdata.exportData(format, params);
        });
        
        // Auto refresh
        if($('.smdata-auto-refresh').length > 0) {
            setInterval(function() {
                location.reload();
            }, 300000); // 5 minutes
        }
    },
    
    /**
     * Initialize date pickers
     */
    initDatePickers: function() {
        if(typeof $.fn.datepicker !== 'undefined') {
            $('.smdata-datepicker').datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true,
                todayHighlight: true
            });
        }
    },
    
    /**
     * Sync data
     */
    syncData: function(publicId, syncType, startDate, endDate, $btn) {
        if(!publicId || !syncType) {
            alert('请选择公众号和同步类型');
            return;
        }
        
        var originalText = $btn.text();
        $btn.text('同步中...').prop('disabled', true);
        
        $.ajax({
            url: createLink('smdata', 'sync'),
            type: 'POST',
            data: {
                publicId: publicId,
                syncType: syncType,
                startDate: startDate,
                endDate: endDate
            },
            dataType: 'json',
            success: function(response) {
                if(response.result === 'success') {
                    alert(response.message || '同步成功');
                    location.reload();
                } else {
                    alert(response.message || '同步失败');
                }
            },
            error: function() {
                alert('网络错误，请重试');
            },
            complete: function() {
                $btn.text(originalText).prop('disabled', false);
            }
        });
    },
    
    /**
     * Export data
     */
    exportData: function(format, params) {
        var url = createLink('smdata', 'export');
        var form = $('<form method="post" action="' + url + '"></form>');
        
        form.append('<input type="hidden" name="format" value="' + format + '">');
        
        for(var key in params) {
            if(params.hasOwnProperty(key)) {
                form.append('<input type="hidden" name="' + key + '" value="' + params[key] + '">');
            }
        }
        
        $('body').append(form);
        form.submit();
        form.remove();
    },
    
    /**
     * Get filter parameters
     */
    getFilterParams: function() {
        var params = {};
        
        $('.smdata-filter-form input, .smdata-filter-form select').each(function() {
            var $input = $(this);
            var name = $input.attr('name');
            var value = $input.val();
            
            if(name && value) {
                params[name] = value;
            }
        });
        
        return params;
    },
    
    /**
     * Draw line chart
     */
    drawLineChart: function(containerId, data, options) {
        if(typeof echarts === 'undefined') {
            console.error('ECharts library not loaded');
            return;
        }
        
        var container = document.getElementById(containerId);
        if(!container) {
            console.error('Container not found: ' + containerId);
            return;
        }
        
        var chart = echarts.init(container);
        
        var defaultOptions = {
            title: {
                text: '',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                top: 30
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: data.dates || []
            },
            yAxis: {
                type: 'value'
            },
            series: data.series || []
        };
        
        var finalOptions = $.extend(true, defaultOptions, options || {});
        chart.setOption(finalOptions);
        
        // Resize chart on window resize
        $(window).on('resize', function() {
            chart.resize();
        });
        
        return chart;
    },
    
    /**
     * Draw pie chart
     */
    drawPieChart: function(containerId, data, options) {
        if(typeof echarts === 'undefined') {
            console.error('ECharts library not loaded');
            return;
        }
        
        var container = document.getElementById(containerId);
        if(!container) {
            console.error('Container not found: ' + containerId);
            return;
        }
        
        var chart = echarts.init(container);
        
        var defaultOptions = {
            title: {
                text: '',
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 10,
                data: data.names || []
            },
            series: [{
                name: data.name || '',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '18',
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: data.data || []
            }]
        };
        
        var finalOptions = $.extend(true, defaultOptions, options || {});
        chart.setOption(finalOptions);
        
        // Resize chart on window resize
        $(window).on('resize', function() {
            chart.resize();
        });
        
        return chart;
    },
    
    /**
     * Format number
     */
    formatNumber: function(num) {
        if(num >= 10000) {
            return (num / 10000).toFixed(1) + '万';
        } else if(num >= 1000) {
            return (num / 1000).toFixed(1) + 'k';
        } else {
            return num.toString();
        }
    },
    
    /**
     * Format date
     */
    formatDate: function(date, format) {
        format = format || 'yyyy-mm-dd';
        
        if(typeof date === 'string') {
            date = new Date(date);
        }
        
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2);
        var day = ('0' + date.getDate()).slice(-2);
        
        return format.replace('yyyy', year).replace('mm', month).replace('dd', day);
    }
};

// Initialize when document ready
$(document).ready(function() {
    smdata.init();
});
