<?php if(!defined("RUN_MODE")) die();?>
<?php
/**
 * The control file of smdata module of chanzhiEPS.
 *
 * @license     ZPLV1.2 (http://zpl.pub/page/zplv12.html)
 * <AUTHOR> Generated
 * @package     smdata
 * @version     $Id$
 * @link        http://www.bestvee.com
 */
class smdata extends control
{
    /**
     * Index page - data overview.
     * 
     * @access public
     * @return void
     */
    public function index()
    {
        $this->app->loadLang('smdata');
        
        /* Get WeChat public accounts */
        $publics = $this->loadModel('wechat')->getList();
        if(empty($publics))
        {
            $this->send(array('result' => 'fail', 'message' => $this->lang->smdata->errors->noPublic));
        }
        
        /* Get default date range */
        $endDate = date('Y-m-d', strtotime('-1 day'));
        $startDate = date('Y-m-d', strtotime('-' . $this->config->smdata->display->defaultDays . ' days'));
        
        /* Get overview data */
        $overviewData = $this->smdata->getOverviewData($publics, $startDate, $endDate);
        
        $this->view->title = $this->lang->smdata->index;
        $this->view->publics = $publics;
        $this->view->overviewData = $overviewData;
        $this->view->startDate = $startDate;
        $this->view->endDate = $endDate;
        $this->display();
    }
    
    /**
     * User analysis page.
     * 
     * @access public
     * @return void
     */
    public function user()
    {
        $this->app->loadLang('smdata');
        
        $publicId = $this->get->publicId ? $this->get->publicId : 0;
        $startDate = $this->get->startDate ? $this->get->startDate : date('Y-m-d', strtotime('-7 days'));
        $endDate = $this->get->endDate ? $this->get->endDate : date('Y-m-d', strtotime('-1 day'));
        
        /* Get WeChat public accounts */
        $publics = $this->loadModel('wechat')->getList();
        if(empty($publics))
        {
            $this->send(array('result' => 'fail', 'message' => $this->lang->smdata->errors->noPublic));
        }
        
        /* Set default public if not specified */
        if(!$publicId) $publicId = key($publics);
        
        /* Get user summary data */
        $userSummaryData = $this->smdata->getUserSummaryData($publicId, $startDate, $endDate);
        
        $this->view->title = $this->lang->smdata->user;
        $this->view->publics = $publics;
        $this->view->publicId = $publicId;
        $this->view->startDate = $startDate;
        $this->view->endDate = $endDate;
        $this->view->userSummaryData = $userSummaryData;
        $this->display();
    }
    
    /**
     * Article analysis page.
     * 
     * @access public
     * @return void
     */
    public function article()
    {
        $this->app->loadLang('smdata');
        
        $publicId = $this->get->publicId ? $this->get->publicId : 0;
        $startDate = $this->get->startDate ? $this->get->startDate : date('Y-m-d', strtotime('-8 days'));
        $endDate = $this->get->endDate ? $this->get->endDate : date('Y-m-d', strtotime('-1 day'));
        
        /* Get WeChat public accounts */
        $publics = $this->loadModel('wechat')->getList();
        if(empty($publics))
        {
            $this->send(array('result' => 'fail', 'message' => $this->lang->smdata->errors->noPublic));
        }
        
        /* Set default public if not specified */
        if(!$publicId) $publicId = key($publics);
        
        /* Get article summary data */
        $articleSummaryData = $this->smdata->getArticleSummaryData($publicId, $startDate, $endDate);
        
        $this->view->title = $this->lang->smdata->article;
        $this->view->publics = $publics;
        $this->view->publicId = $publicId;
        $this->view->startDate = $startDate;
        $this->view->endDate = $endDate;
        $this->view->articleSummaryData = $articleSummaryData;
        $this->display();
    }
    
    /**
     * Message analysis page.
     * 
     * @access public
     * @return void
     */
    public function message()
    {
        $this->app->loadLang('smdata');
        
        $publicId = $this->get->publicId ? $this->get->publicId : 0;
        $startDate = $this->get->startDate ? $this->get->startDate : date('Y-m-d', strtotime('-7 days'));
        $endDate = $this->get->endDate ? $this->get->endDate : date('Y-m-d', strtotime('-1 day'));
        
        /* Get WeChat public accounts */
        $publics = $this->loadModel('wechat')->getList();
        if(empty($publics))
        {
            $this->send(array('result' => 'fail', 'message' => $this->lang->smdata->errors->noPublic));
        }
        
        /* Set default public if not specified */
        if(!$publicId) $publicId = key($publics);
        
        /* Get upstream message data */
        $upstreamMsgData = $this->smdata->getUpstreamMsgData($publicId, $startDate, $endDate);
        
        $this->view->title = $this->lang->smdata->message;
        $this->view->publics = $publics;
        $this->view->publicId = $publicId;
        $this->view->startDate = $startDate;
        $this->view->endDate = $endDate;
        $this->view->upstreamMsgData = $upstreamMsgData;
        $this->display();
    }
    
    /**
     * Sync log page.
     * 
     * @access public
     * @return void
     */
    public function synclog()
    {   
        $publicId = $this->get->publicId ? $this->get->publicId : 0;
        $syncType = $this->get->syncType ? $this->get->syncType : '';
        
        /* Get WeChat public accounts */
        $publics = $this->loadModel('wechat')->getList();
        
        /* Get sync logs */
        $this->app->loadClass('pager', true);
        $pager = new pager(0, $this->config->smdata->display->pageSize, $this->get->pageID);
        $syncLogs = $this->smdata->getSyncLogs($publicId, $syncType, $pager);
        
        $this->view->title = $this->lang->smdata->synclog;
        $this->view->publics = $publics;
        $this->view->publicId = $publicId;
        $this->view->syncType = $syncType;
        $this->view->syncLogs = $syncLogs;
        $this->view->pager = $pager;
        $this->display();
    }
    
    /**
     * Manual sync data.
     * 
     * @access public
     * @return void
     */
    public function sync()
    {
        $this->app->loadLang('smdata');
        
        $publicId = $this->post->publicId ? $this->post->publicId : 0;
        $syncType = $this->post->syncType ? $this->post->syncType : '';
        $startDate = $this->post->startDate ? $this->post->startDate : date('Y-m-d', strtotime('-1 day'));
        $endDate = $this->post->endDate ? $this->post->endDate : date('Y-m-d', strtotime('-1 day'));
        
        if(!$publicId || !$syncType)
        {
            $this->send(array('result' => 'fail', 'message' => $this->lang->smdata->errors->selectPublic));
        }
        
        /* Execute sync */
        $result = false;
        switch($syncType)
        {
            case 'user_summary':
                $result = $this->smdata->syncUserSummary($publicId, $startDate, $endDate);
                break;
            case 'article_summary':
                $result = $this->smdata->syncArticleSummary($publicId, $startDate, $endDate);
                break;
            case 'upstream_msg':
                $result = $this->smdata->syncUpstreamMsg($publicId, $startDate, $endDate);
                break;
            case 'all':
                $result1 = $this->smdata->syncUserSummary($publicId, $startDate, $endDate);
                $result2 = $this->smdata->syncArticleSummary($publicId, $startDate, $endDate);
                $result3 = $this->smdata->syncUpstreamMsg($publicId, $startDate, $endDate);
                $result = $result1 && $result2 && $result3;
                break;
        }
        
        if($result)
        {
            $this->send(array('result' => 'success', 'message' => $this->lang->smdata->tips->syncSuccess));
        }
        else
        {
            $this->send(array('result' => 'fail', 'message' => $this->lang->smdata->tips->syncFailed));
        }
    }
}
