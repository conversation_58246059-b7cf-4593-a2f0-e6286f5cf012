<?php
/**
 * Xdebug 测试文件
 * 
 * 用于验证 Xdebug 配置是否正确
 * 访问: http://localhost:8080/test_xdebug.php
 */

// 设置断点在这一行 - 测试基本调试功能
$message = "Hello Xdebug!";
echo "<h1>$message</h1>";

// 测试变量调试
$testArray = [
    'name' => 'Social Media Data',
    'version' => '2.0',
    'php_version' => PHP_VERSION,
    'timestamp' => date('Y-m-d H:i:s')
];

// 设置断点在这一行 - 测试数组变量查看
$testObject = new stdClass();
$testObject->property1 = 'value1';
$testObject->property2 = 'value2';
$testObject->nested = $testArray;

echo "<h2>测试数据:</h2>";
echo "<pre>";
print_r($testArray);
echo "</pre>";

// 测试函数调用调试
function testFunction($param1, $param2) {
    // 设置断点在这一行 - 测试函数内部调试
    $result = $param1 + $param2;
    return $result;
}

$sum = testFunction(10, 20);
echo "<p>函数调用结果: $sum</p>";

// 测试异常调试
try {
    // 设置断点在这一行 - 测试异常处理调试
    throw new Exception("这是一个测试异常");
} catch (Exception $e) {
    echo "<p style='color: red;'>捕获异常: " . $e->getMessage() . "</p>";
}

// 显示 Xdebug 信息
echo "<h2>Xdebug 配置信息:</h2>";
if (extension_loaded('xdebug')) {
    echo "<p style='color: green;'>✅ Xdebug 已加载</p>";
    
    // 显示 Xdebug 版本
    echo "<p>Xdebug 版本: " . phpversion('xdebug') . "</p>";
    
    // 显示 Xdebug 模式
    if (function_exists('xdebug_info')) {
        echo "<h3>Xdebug 详细信息:</h3>";
        echo "<pre>";
        xdebug_info();
        echo "</pre>";
    }
    
    // 显示相关配置
    $xdebugSettings = [
        'xdebug.mode',
        'xdebug.start_with_request',
        'xdebug.client_host',
        'xdebug.client_port',
        'xdebug.idekey',
        'xdebug.log'
    ];
    
    echo "<h3>Xdebug 配置:</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>配置项</th><th>值</th></tr>";
    
    foreach ($xdebugSettings as $setting) {
        $value = ini_get($setting);
        echo "<tr><td>$setting</td><td>" . ($value ?: '未设置') . "</td></tr>";
    }
    echo "</table>";
    
} else {
    echo "<p style='color: red;'>❌ Xdebug 未加载</p>";
}

// 显示 PHP 基本信息
echo "<h2>PHP 环境信息:</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>项目</th><th>值</th></tr>";
echo "<tr><td>PHP 版本</td><td>" . PHP_VERSION . "</td></tr>";
echo "<tr><td>服务器软件</td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . "</td></tr>";
echo "<tr><td>文档根目录</td><td>" . ($_SERVER['DOCUMENT_ROOT'] ?? '未知') . "</td></tr>";
echo "<tr><td>当前脚本</td><td>" . __FILE__ . "</td></tr>";
echo "<tr><td>内存限制</td><td>" . ini_get('memory_limit') . "</td></tr>";
echo "<tr><td>最大执行时间</td><td>" . ini_get('max_execution_time') . " 秒</td></tr>";
echo "</table>";

// 测试数据库连接（使用生产数据库配置）
echo "<h2>数据库连接测试:</h2>";
try {
    // 尝试连接数据库
    $configFile = '../system/config/my.php';
    if (file_exists($configFile)) {
        $config = include $configFile;
        if (isset($config->db)) {
            $dsn = "mysql:host={$config->db->host};port={$config->db->port};dbname={$config->db->name}";
            $pdo = new PDO($dsn, $config->db->user, $config->db->password);
            echo "<p style='color: green;'>✅ 生产数据库连接成功</p>";

            // 测试查询
            $stmt = $pdo->query("SELECT VERSION() as version");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p>MySQL 版本: " . $result['version'] . "</p>";

            // 显示数据库信息（不显示敏感信息）
            echo "<p>数据库主机: " . $config->db->host . ":" . $config->db->port . "</p>";
            echo "<p>数据库名称: " . $config->db->name . "</p>";

        } else {
            echo "<p style='color: orange;'>⚠️ 数据库配置未找到</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ 配置文件不存在: $configFile</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
    echo "<p style='color: orange;'>💡 这是正常的，因为开发环境使用生产数据库配置</p>";
}

// 测试微信数据统计模块
echo "<h2>微信数据统计模块测试:</h2>";
try {
    // 检查模块文件是否存在
    $modelFile = '../system/module/smdata/model.php';
    if (file_exists($modelFile)) {
        echo "<p style='color: green;'>✅ smdata 模块文件存在</p>";
        
        // 检查 EasyWeChat 是否可用
        $composerAutoload = '../vendor/autoload.php';
        if (file_exists($composerAutoload)) {
            require_once $composerAutoload;
            echo "<p style='color: green;'>✅ Composer 自动加载成功</p>";
            
            // 检查 EasyWeChat 类是否存在
            if (class_exists('\EasyWeChat\OfficialAccount\Application')) {
                echo "<p style='color: green;'>✅ EasyWeChat 6 类可用</p>";
            } else {
                echo "<p style='color: red;'>❌ EasyWeChat 6 类不可用</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Composer 自动加载文件不存在</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ smdata 模块文件不存在</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 模块测试失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>调试说明:</h2>";
echo "<ol>";
echo "<li>在 IDE 中设置断点（点击行号左侧）</li>";
echo "<li>启动 IDE 的调试监听（通常是点击调试按钮或按 F5）</li>";
echo "<li>刷新此页面，IDE 应该会在断点处停止</li>";
echo "<li>可以查看变量值、单步执行、查看调用栈等</li>";
echo "</ol>";

echo "<p><strong>提示:</strong> 如果调试不工作，请检查:</p>";
echo "<ul>";
echo "<li>IDE 是否正在监听端口 9003</li>";
echo "<li>路径映射是否正确配置</li>";
echo "<li>防火墙是否阻止了连接</li>";
echo "<li>查看 Xdebug 日志: <code>docker exec -it social_media_data_web_dev cat /var/log/app/xdebug.log</code></li>";
echo "</ul>";

// 最后一行 - 设置断点测试调试结束
echo "<p style='color: blue; font-weight: bold;'>🎉 Xdebug 测试完成！</p>";
?>
